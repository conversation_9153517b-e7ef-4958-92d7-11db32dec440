'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import {
  Modal,
  TextInput,
  NumberInput,
  Button,
  Group,
  Text,
  Stack,
  Alert,
  Divider,
  Badge
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconAlertCircle, IconCoins, IconArrowRight } from '@tabler/icons-react';
import { transferPoints, getUserPoints } from '../../lib/points';
import { isValidODudeName } from '../../lib/common';

interface TransferPointsModalProps {
  opened: boolean;
  onClose: () => void;
  onTransferComplete?: () => void;
}

interface TransferFormValues {
  recipientName: string;
  amount: number;
  description: string;
}

export function TransferPointsModal({ opened, onClose, onTransferComplete }: TransferPointsModalProps) {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);
  const [userPoints, setUserPoints] = useState<number>(0);

  const form = useForm<TransferFormValues>({
    initialValues: {
      recipientName: '',
      amount: 0,
      description: ''
    },
    validate: {
      recipientName: (value) => {
        if (!value.trim()) return 'Recipient ODude name is required';
        if (!isValidODudeName(value.trim())) return 'Please enter a valid ODude name';
        if (value.trim().toLowerCase() === session?.user?.email?.split('@')[0]) {
          return 'You cannot transfer points to yourself';
        }
        return null;
      },
      amount: (value) => {
        if (!value || value <= 0) return 'Amount must be greater than 0';
        if (value > userPoints) return 'Insufficient points';
        if (!Number.isInteger(value)) return 'Amount must be a whole number';
        return null;
      }
    }
  });

  const fetchUserPoints = async () => {
    if (!session?.user?.email) return;
    
    try {
      const points = await getUserPoints(session.user.email);
      setUserPoints(points);
    } catch (error) {
      console.error('Error fetching user points:', error);
    }
  };

  const handleModalOpen = () => {
    if (opened) {
      fetchUserPoints();
      form.reset();
    }
  };

  // Fetch points when modal opens
  useState(() => {
    handleModalOpen();
  });

  const handleSubmit = async (values: TransferFormValues) => {
    if (!session?.user?.email) {
      notifications.show({
        title: 'Error',
        message: 'You must be logged in to transfer points',
        color: 'red',
      });
      return;
    }

    setLoading(true);

    try {
      const result = await transferPoints(
        session.user.email,
        values.recipientName.trim(),
        values.amount,
        values.description.trim() || undefined
      );

      if (result.success) {
        notifications.show({
          title: 'Transfer Successful',
          message: result.message,
          color: 'green',
        });

        form.reset();
        onClose();
        
        if (onTransferComplete) {
          onTransferComplete();
        }
      } else {
        notifications.show({
          title: 'Transfer Failed',
          message: result.message,
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Transfer error:', error);
      notifications.show({
        title: 'Error',
        message: 'An unexpected error occurred during the transfer',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title="Transfer Points"
      size="md"
      centered
    >
      <form onSubmit={form.onSubmit(handleSubmit)}>
        <Stack gap="md">
          <Alert icon={<IconCoins size={16} />} color="blue" variant="light">
            <Group justify="space-between">
              <Text size="sm">Your current balance:</Text>
              <Badge size="lg" variant="filled" color="blue">
                {userPoints.toLocaleString()} points
              </Badge>
            </Group>
          </Alert>

          <TextInput
            label="Recipient ODude Name"
            placeholder="Enter ODude name (e.g., john.me)"
            required
            {...form.getInputProps('recipientName')}
            leftSection={<Text size="sm" c="dimmed">@</Text>}
          />

          <NumberInput
            label="Amount"
            placeholder="Enter amount to transfer"
            required
            min={1}
            max={userPoints}
            {...form.getInputProps('amount')}
            leftSection={<IconCoins size={16} />}
          />

          <TextInput
            label="Description (Optional)"
            placeholder="Add a note for this transfer"
            {...form.getInputProps('description')}
          />

          {form.values.amount > 0 && form.values.recipientName && (
            <>
              <Divider />
              <Group justify="center" gap="xs">
                <Text size="sm" c="dimmed">You will send</Text>
                <Badge color="orange">{form.values.amount} points</Badge>
                <IconArrowRight size={16} />
                <Text size="sm" c="dimmed">to</Text>
                <Badge color="green">{form.values.recipientName}</Badge>
              </Group>
            </>
          )}

          <Group justify="flex-end" mt="md">
            <Button variant="subtle" onClick={onClose} disabled={loading}>
              Cancel
            </Button>
            <Button
              type="submit"
              loading={loading}
              disabled={!form.isValid() || form.values.amount <= 0}
            >
              Transfer Points
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
}

export default TransferPointsModal;
